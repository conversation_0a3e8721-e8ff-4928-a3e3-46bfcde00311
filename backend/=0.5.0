Requirement already satisfied: composio-core in /Users/<USER>/miniconda/lib/python3.12/site-packages (0.7.15)
Requirement already satisfied: aiohttp in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (3.11.16)
Requirement already satisfied: requests<3,>=2.31.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (2.32.3)
Requirement already satisfied: jsonschema<5,>=4.21.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (4.23.0)
Requirement already satisfied: sentry-sdk>=2.0.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (2.28.0)
Requirement already satisfied: pysher==1.0.8 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (1.0.8)
Requirement already satisfied: pydantic>=2.6.4 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (2.11.7)
Requirement already satisfied: importlib-metadata>=4.8.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (8.5.0)
Requirement already satisfied: jsonref>=1.1.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (1.1.0)
Requirement already satisfied: inflection>=0.5.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (0.5.1)
Requirement already satisfied: semver>=2.13.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (3.0.4)
Requirement already satisfied: click in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (8.1.7)
Requirement already satisfied: rich<14,>=13.7.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (13.9.4)
Requirement already satisfied: pyperclip<2,>=1.8.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (1.9.0)
Requirement already satisfied: paramiko>=3.4.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (3.5.1)
Requirement already satisfied: fastapi in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (0.115.13)
Requirement already satisfied: uvicorn in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (0.27.1)
Requirement already satisfied: pyyaml>=6.0.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from composio-core) (6.0.2)
Requirement already satisfied: websocket-client!=0.49 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from pysher==1.0.8->composio-core) (1.7.0)
Requirement already satisfied: zipp>=3.20 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from importlib-metadata>=4.8.1->composio-core) (3.21.0)
Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from jsonschema<5,>=4.21.1->composio-core) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from jsonschema<5,>=4.21.1->composio-core) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from jsonschema<5,>=4.21.1->composio-core) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from jsonschema<5,>=4.21.1->composio-core) (0.24.0)
Requirement already satisfied: bcrypt>=3.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from paramiko>=3.4.1->composio-core) (4.3.0)
Requirement already satisfied: cryptography>=3.3 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from paramiko>=3.4.1->composio-core) (43.0.3)
Requirement already satisfied: pynacl>=1.5 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from paramiko>=3.4.1->composio-core) (1.5.0)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from pydantic>=2.6.4->composio-core) (0.6.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from pydantic>=2.6.4->composio-core) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from pydantic>=2.6.4->composio-core) (4.13.1)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from pydantic>=2.6.4->composio-core) (0.4.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from requests<3,>=2.31.0->composio-core) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from requests<3,>=2.31.0->composio-core) (3.6)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from requests<3,>=2.31.0->composio-core) (2.2.1)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from requests<3,>=2.31.0->composio-core) (2024.2.2)
Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from rich<14,>=13.7.1->composio-core) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from rich<14,>=13.7.1->composio-core) (2.17.2)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (2.4.6)
Requirement already satisfied: aiosignal>=1.1.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (1.3.1)
Requirement already satisfied: frozenlist>=1.1.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (1.4.1)
Requirement already satisfied: multidict<7.0,>=4.5 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (6.0.5)
Requirement already satisfied: propcache>=0.2.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (0.2.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from aiohttp->composio-core) (1.19.0)
Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from fastapi->composio-core) (0.46.2)
Requirement already satisfied: h11>=0.8 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from uvicorn->composio-core) (0.16.0)
Requirement already satisfied: cffi>=1.12 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from cryptography>=3.3->paramiko>=3.4.1->composio-core) (1.16.0)
Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from markdown-it-py>=2.2.0->rich<14,>=13.7.1->composio-core) (0.1.2)
Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from starlette<0.47.0,>=0.40.0->fastapi->composio-core) (4.9.0)
Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/miniconda/lib/python3.12/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi->composio-core) (1.3.0)
Requirement already satisfied: pycparser in /Users/<USER>/miniconda/lib/python3.12/site-packages (from cffi>=1.12->cryptography>=3.3->paramiko>=3.4.1->composio-core) (2.21)
