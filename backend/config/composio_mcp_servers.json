{"servers": {"gmail": {"app_key": "gmail", "server_id": "3adcafb4-4a7d-4b97-bb26-3be782883e06", "server_name": "Gmail Integration Server", "base_mcp_url": "https://mcp.composio.dev/composio/server/3adcafb4-4a7d-4b97-bb26-3be782883e06", "description": "Gmail integration for email management"}, "github": {"app_key": "github", "server_id": "your-github-server-id-from-dashboard", "server_name": "GitHub Integration Server", "base_mcp_url": "https://mcp.composio.dev/composio/server/your-github-server-id", "description": "GitHub integration for repository management"}, "slack": {"app_key": "slack", "server_id": "your-slack-server-id-from-dashboard", "server_name": "Slack Integration Server", "base_mcp_url": "https://mcp.composio.dev/composio/server/your-slack-server-id", "description": "Slack integration for team communication"}, "notion": {"app_key": "notion", "server_id": "5b1d27a8-c112-470a-af56-3f1c40991ebe", "server_name": "Notion Integration Server", "base_mcp_url": "https://mcp.composio.dev/composio/server/5b1d27a8-c112-470a-af56-3f1c40991ebe", "description": "Notion integration for knowledge management"}, "linear": {"app_key": "linear", "server_id": "your-linear-server-id-from-dashboard", "server_name": "Linear Integration Server", "base_mcp_url": "https://mcp.composio.dev/composio/server/your-linear-server-id", "description": "Linear integration for project management"}}, "metadata": {"version": "1.0", "description": "Registry of MCP servers created in Composio dashboard", "last_updated": "2025-06-26", "instructions": ["1. Create MCP servers in your Composio dashboard", "2. Copy the server IDs and URLs from the dashboard", "3. Update this configuration file with your actual server details", "4. The server_id should match the ID from your Composio dashboard", "5. The base_mcp_url should be the base URL without query parameters"]}}