#!/usr/bin/env python3
"""
Test script for the new Composio API integration

This script tests the new API-based approach for creating MCP servers
and generating user-specific URLs.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from services.composio_api_client import composio_api_client
from services.composio_mcp_manager import composio_mcp_manager
from utils.logger import logger


async def test_composio_api_client():
    """Test the Composio API client functionality"""
    print("🧪 Testing Composio API Client")
    print("=" * 50)
    
    # Check if API key is configured
    if not composio_api_client.api_key:
        print("❌ COMPOSIO_API_KEY not configured")
        print("Please set the COMPOSIO_API_KEY environment variable")
        return False
    
    print(f"✅ API Key configured: {composio_api_client.api_key[:10]}...")
    print(f"✅ Base URL: {composio_api_client.base_url}")
    
    try:
        # Test 1: List existing MCP servers
        print("\n📋 Test 1: Listing existing MCP servers")
        servers = await composio_api_client.list_mcp_servers()
        print(f"Found {len(servers)} existing MCP servers")
        
        for server in servers:
            print(f"  - {server.name} (ID: {server.id})")
            print(f"    URL: {server.mcp_url}")
            print(f"    Toolkits: {server.toolkits}")
        
        # Test 2: Create a new MCP server for Gmail
        print("\n🏗️  Test 2: Creating new MCP server for Gmail")
        try:
            gmail_server = await composio_api_client.create_mcp_server(
                name="Gmail Test Server",
                toolkits=["gmail"]
            )
            print(f"✅ Created Gmail MCP server: {gmail_server.id}")
            print(f"   URL: {gmail_server.mcp_url}")
            print(f"   Commands: {gmail_server.commands}")
            
        except Exception as e:
            print(f"⚠️  Could not create Gmail server (might already exist): {e}")
        
        # Test 3: List connected accounts
        print("\n🔗 Test 3: Listing connected accounts")
        accounts = await composio_api_client.list_connected_accounts(
            toolkit_slugs=["gmail"],
            statuses=["ACTIVE"]
        )
        print(f"Found {len(accounts)} active Gmail connections")
        
        for account in accounts:
            print(f"  - Account ID: {account.id}")
            print(f"    Status: {account.status}")
            print(f"    Toolkit: {account.toolkit_slug}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Composio API client: {e}")
        return False


async def test_composio_mcp_manager():
    """Test the Composio MCP manager functionality"""
    print("\n🎯 Testing Composio MCP Manager")
    print("=" * 50)
    
    try:
        # Test user ID (you can change this to test with different users)
        test_user_id = "test-user-123"
        test_app_key = "gmail"
        
        print(f"Testing with user_id: {test_user_id}, app_key: {test_app_key}")
        
        # Test 1: Generate user MCP URL
        print("\n🔗 Test 1: Generating user-specific MCP URL")
        connection = await composio_mcp_manager.generate_user_mcp_url(
            test_user_id, test_app_key
        )
        
        print(f"Status: {connection.status}")
        print(f"MCP URL: {connection.mcp_url}")
        print(f"Server ID: {connection.server_id}")
        print(f"Connected Account ID: {connection.connected_account_id}")
        
        # Test 2: List supported apps
        print("\n📱 Test 2: Listing supported apps")
        supported_apps = await composio_mcp_manager.list_supported_apps()
        print(f"Supported apps: {supported_apps}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Composio MCP manager: {e}")
        return False


async def main():
    """Main test function"""
    print("🚀 Starting Composio API Integration Tests")
    print("=" * 60)
    
    # Test API client
    api_client_success = await test_composio_api_client()
    
    # Test MCP manager
    mcp_manager_success = await test_composio_mcp_manager()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    print(f"API Client Tests: {'✅ PASSED' if api_client_success else '❌ FAILED'}")
    print(f"MCP Manager Tests: {'✅ PASSED' if mcp_manager_success else '❌ FAILED'}")
    
    if api_client_success and mcp_manager_success:
        print("\n🎉 All tests passed! The new Composio API integration is working.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    return api_client_success and mcp_manager_success


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run the tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
