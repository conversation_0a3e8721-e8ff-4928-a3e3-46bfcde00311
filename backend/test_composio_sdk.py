#!/usr/bin/env python3
"""
Test script for Composio SDK integration

This script tests the new Composio SDK-based connection manager.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from services.composio_connection_manager import composio_connection_manager
from services.composio_url_generator import composio_url_generator, URLPhase
from utils.logger import logger


async def test_composio_sdk():
    """Test Composio SDK functionality"""
    print("🧪 Testing Composio SDK Integration")
    print("=" * 50)
    
    # Test 1: Check SDK availability
    print("\n📦 Test 1: Checking SDK availability")
    is_available = composio_connection_manager.is_sdk_available()
    print(f"SDK Available: {'✅ YES' if is_available else '❌ NO'}")
    
    if not is_available:
        print("⚠️  Composio SDK not available - skipping connection tests")
        return False
    
    # Test 2: Test connection retrieval
    print("\n🔗 Test 2: Testing connection retrieval")
    test_user_id = "test-user-sdk-123"
    
    try:
        connections = await composio_connection_manager.get_user_connections(test_user_id)
        print(f"Found {len(connections)} connections for user {test_user_id}")
        
        for conn in connections:
            print(f"  - {conn.app_name}: {conn.id} (status: {conn.status})")
    
    except Exception as e:
        print(f"⚠️  Error getting connections: {e}")
    
    # Test 3: Test connection status checking
    print("\n📊 Test 3: Testing connection status")
    try:
        gmail_status = await composio_connection_manager.check_user_connection_status(
            test_user_id, "gmail"
        )
        print(f"Gmail connection status:")
        print(f"  Has connection: {gmail_status.has_connection}")
        print(f"  Connection ID: {gmail_status.connection_id}")
        print(f"  Entity ID: {gmail_status.entity_id}")
        
    except Exception as e:
        print(f"⚠️  Error checking connection status: {e}")
    
    return True


def test_url_generator():
    """Test URL generator functionality"""
    print("\n🔗 Testing URL Generator")
    print("=" * 50)
    
    server_id = "3adcafb4-4a7d-4b97-bb26-3be782883e06"
    user_id = "test-user-123"
    connection_id = "ca_PU0-B9237c2v"
    
    # Test 1: Helper URL generation
    print("\n📧 Test 1: Helper URL generation")
    helper_url = composio_url_generator.generate_connection_helper_url(server_id, user_id)
    print(f"Helper URL: {helper_url.url}")
    print(f"Phase: {helper_url.phase.value}")
    print(f"Description: {helper_url.description}")
    
    # Test 2: Tool calling URL generation
    print("\n🛠️  Test 2: Tool calling URL generation")
    tool_url = composio_url_generator.generate_tool_calling_url(server_id, connection_id)
    print(f"Tool URL: {tool_url.url}")
    print(f"Phase: {tool_url.phase.value}")
    print(f"Description: {tool_url.description}")
    
    # Test 3: URL parsing
    print("\n🔍 Test 3: URL parsing")
    extracted_server_id = composio_url_generator.extract_server_id_from_url(tool_url.url)
    extracted_connection_id = composio_url_generator.extract_connected_account_id_from_url(tool_url.url)
    url_phase = composio_url_generator.identify_url_phase(tool_url.url)
    
    print(f"Extracted server ID: {extracted_server_id}")
    print(f"Extracted connection ID: {extracted_connection_id}")
    print(f"Identified phase: {url_phase.value}")
    
    # Test 4: URL upgrade
    print("\n⬆️  Test 4: URL upgrade")
    upgraded_url = composio_url_generator.upgrade_helper_url_to_tool_calling(
        helper_url.url, connection_id
    )
    if upgraded_url:
        print(f"Upgraded URL: {upgraded_url.url}")
        print(f"New phase: {upgraded_url.phase.value}")
    else:
        print("❌ Failed to upgrade URL")
    
    return True


async def main():
    """Main test function"""
    print("🚀 Starting Composio SDK and URL Generator Tests")
    print("=" * 60)
    
    # Test SDK functionality
    sdk_success = await test_composio_sdk()
    
    # Test URL generator
    url_success = test_url_generator()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    print(f"SDK Tests: {'✅ PASSED' if sdk_success else '❌ FAILED'}")
    print(f"URL Generator Tests: {'✅ PASSED' if url_success else '❌ FAILED'}")
    
    if sdk_success and url_success:
        print("\n🎉 All tests passed! SDK integration is working.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    return sdk_success and url_success


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run the tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
