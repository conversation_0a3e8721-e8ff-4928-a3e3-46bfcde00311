#!/usr/bin/env python3
"""
Test script for the new per-user MCP instantiation approach

This script tests the registry-based approach where MCP servers
are created in the Composio dashboard and then instantiated per user.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from services.composio_mcp_manager import composio_mcp_manager, MCPServerConfig
from utils.logger import logger


async def test_mcp_manager_registry():
    """Test the MCP manager with registry-based approach"""
    print("🧪 Testing MCP Manager - Registry-Based Approach")
    print("=" * 60)
    
    # Test 1: Check initial registry state
    print("\n📋 Test 1: Checking initial registry state")
    configured_apps = composio_mcp_manager.list_configured_apps()
    print(f"Configured apps: {configured_apps}")
    
    supported_apps = await composio_mcp_manager.list_supported_apps()
    print(f"Supported apps: {supported_apps}")
    
    # Test 2: Add a test server configuration
    print("\n🏗️  Test 2: Adding test server configuration")
    test_config = MCPServerConfig(
        app_key="test-gmail",
        server_id="test-server-123",
        server_name="Test Gmail Server",
        base_mcp_url="https://mcp.composio.dev/composio/server/test-server-123",
        description="Test Gmail integration"
    )
    
    composio_mcp_manager.add_mcp_server_config(test_config)
    print(f"✅ Added test config for {test_config.app_key}")
    
    # Test 3: Check if config was added
    print("\n🔍 Test 3: Verifying config was added")
    retrieved_config = composio_mcp_manager.get_mcp_server_config("test-gmail")
    if retrieved_config:
        print(f"✅ Retrieved config: {retrieved_config.server_name}")
        print(f"   Server ID: {retrieved_config.server_id}")
        print(f"   Base URL: {retrieved_config.base_mcp_url}")
    else:
        print("❌ Failed to retrieve test config")
        return False
    
    # Test 4: Test user URL generation (will fail without API key, but should show the flow)
    print("\n🔗 Test 4: Testing user URL generation")
    test_user_id = "test-user-456"
    
    try:
        connection = await composio_mcp_manager.generate_user_mcp_url(
            test_user_id, "test-gmail"
        )
        
        print(f"Status: {connection.status}")
        print(f"MCP URL: {connection.mcp_url}")
        print(f"Server ID: {connection.server_id}")
        print(f"Connected Account ID: {connection.connected_account_id}")
        
        if connection.status == "error":
            print("⚠️  Expected error (likely due to API configuration)")
        else:
            print("✅ URL generation successful")
            
    except Exception as e:
        print(f"⚠️  Expected error during URL generation: {e}")
    
    # Test 5: Test with non-existent app
    print("\n❌ Test 5: Testing with non-existent app")
    connection = await composio_mcp_manager.generate_user_mcp_url(
        test_user_id, "non-existent-app"
    )
    
    if connection.status == "error":
        print("✅ Correctly handled non-existent app")
    else:
        print("❌ Should have failed for non-existent app")
        return False
    
    return True


def test_config_file_loading():
    """Test configuration file loading"""
    print("\n📁 Testing Configuration File Loading")
    print("=" * 50)
    
    config_path = os.path.join(os.path.dirname(__file__), "config", "composio_mcp_servers.json")
    
    if os.path.exists(config_path):
        print(f"✅ Config file exists: {config_path}")
        
        try:
            import json
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            servers = config_data.get("servers", {})
            print(f"✅ Found {len(servers)} server configurations")
            
            for app_key, server_data in servers.items():
                server_id = server_data.get("server_id", "")
                if "your-" in server_id:
                    print(f"⚠️  {app_key}: Placeholder configuration (needs real server ID)")
                else:
                    print(f"✅ {app_key}: Configured with server ID {server_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error reading config file: {e}")
            return False
    else:
        print(f"❌ Config file not found: {config_path}")
        return False


async def main():
    """Main test function"""
    print("🚀 Starting MCP Manager Tests - Per-User Instantiation")
    print("=" * 70)
    
    # Test config file loading
    config_success = test_config_file_loading()
    
    # Test MCP manager
    manager_success = await test_mcp_manager_registry()
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary")
    print("=" * 70)
    print(f"Config File Loading: {'✅ PASSED' if config_success else '❌ FAILED'}")
    print(f"MCP Manager Tests: {'✅ PASSED' if manager_success else '❌ FAILED'}")
    
    if config_success and manager_success:
        print("\n🎉 All tests passed! The new per-user instantiation approach is working.")
        print("\n📝 Next Steps:")
        print("1. Create MCP servers in your Composio dashboard")
        print("2. Update config/composio_mcp_servers.json with real server IDs")
        print("3. Test with real user connections")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    return config_success and manager_success


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run the tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
