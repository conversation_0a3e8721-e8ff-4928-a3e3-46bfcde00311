"""
Composio MCP Manager Service

This service implements the correct Composio MCP flow:
1. Create/retrieve MCP servers via API
2. Manage connected accounts for users
3. Generate user-specific URLs with proper query parameters

This replaces the static constants-based approach with proper API integration.
"""

import json
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from utils.logger import logger
from supabase import create_client, Client
from utils.config import config
from services.composio_api_client import (
    composio_api_client,
    ComposioMCPServer,
    ComposioConnectedAccount,
)
import os


# Supabase configuration
SUPABASE_URL = config.SUPABASE_URL
SUPABASE_SERVICE_KEY = config.SUPABASE_SERVICE_ROLE_KEY


@dataclass
class MCPServerInfo:
    """Information about an MCP server stored in our database"""

    app_key: str
    server_id: str
    server_name: str
    base_mcp_url: str
    toolkits: List[str]
    created_at: str
    updated_at: str


@dataclass
class UserMCPConnection:
    """User-specific MCP connection with generated URL"""

    app_key: str
    user_id: str
    mcp_url: str
    connected_account_id: Optional[str]
    server_id: str
    status: str  # 'active', 'pending', 'error'


class ComposioMCPManager:
    """Manager for Composio MCP servers and user connections"""

    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        self.api_client = composio_api_client

        # Table name for storing MCP server information
        self.mcp_servers_table = "composio_mcp_servers"

    async def ensure_mcp_server_exists(self, app_key: str) -> MCPServerInfo:
        """
        Ensure an MCP server exists for the given app.
        Creates one via API if it doesn't exist, otherwise returns existing server info.
        """
        try:
            # Check if we already have this server in our database
            existing_server = await self._get_stored_server_info(app_key)
            if existing_server:
                logger.info(
                    f"Found existing MCP server for {app_key}: {existing_server.server_id}"
                )
                return existing_server

            # Create new MCP server via Composio API
            logger.info(f"Creating new MCP server for app: {app_key}")

            # Check if API key is configured
            if not self.api_client.api_key:
                raise ValueError(
                    "COMPOSIO_API_KEY not configured. Please set the environment variable."
                )

            server = await self.api_client.create_mcp_server(
                name=f"{app_key.title()} Integration Server", toolkits=[app_key]
            )

            # Store server info in our database
            server_info = MCPServerInfo(
                app_key=app_key,
                server_id=server.id,
                server_name=server.name,
                base_mcp_url=server.mcp_url,
                toolkits=server.toolkits,
                created_at=server.created_at,
                updated_at=server.updated_at,
            )

            await self._store_server_info(server_info)
            logger.info(f"Created and stored MCP server for {app_key}: {server.id}")

            return server_info

        except Exception as e:
            logger.error(f"Error ensuring MCP server exists for {app_key}: {e}")
            raise

    async def get_user_connected_accounts(
        self, user_id: str, app_key: Optional[str] = None
    ) -> List[ComposioConnectedAccount]:
        """Get connected accounts for a user, optionally filtered by app"""
        try:
            # Map Atlas user ID to Composio entity ID
            entity_id = self._map_user_to_entity_id(user_id)

            toolkit_slugs = [app_key] if app_key else None

            accounts = await self.api_client.list_connected_accounts(
                user_ids=[entity_id],
                toolkit_slugs=toolkit_slugs,
                statuses=["ACTIVE"],  # Only get active connections
            )

            logger.info(f"Found {len(accounts)} connected accounts for user {user_id}")
            return accounts

        except Exception as e:
            logger.error(f"Error getting connected accounts for user {user_id}: {e}")
            return []

    async def generate_user_mcp_url(
        self, user_id: str, app_key: str
    ) -> UserMCPConnection:
        """
        Generate a user-specific MCP URL with proper query parameters.
        This implements the correct Composio flow.
        """
        try:
            # Step 1: Ensure MCP server exists
            server_info = await self.ensure_mcp_server_exists(app_key)

            # Step 2: Get user's connected accounts for this app
            connected_accounts = await self.get_user_connected_accounts(
                user_id, app_key
            )

            # Step 3: Map Atlas user ID to Composio entity ID
            entity_id = self._map_user_to_entity_id(user_id)

            # Step 4: Generate user-specific URL
            if connected_accounts:
                # User has active connection - use connected_account_id
                connected_account = connected_accounts[0]  # Use first active connection

                generated_url = await self.api_client.generate_mcp_url(
                    mcp_server_id=server_info.server_id,
                    user_ids=[entity_id],
                    connected_account_ids=[connected_account.id],
                )

                # Use the connected account URL
                final_url = (
                    generated_url.connected_account_urls[0]
                    if generated_url.connected_account_urls
                    else generated_url.mcp_url
                )

                return UserMCPConnection(
                    app_key=app_key,
                    user_id=user_id,
                    mcp_url=final_url,
                    connected_account_id=connected_account.id,
                    server_id=server_info.server_id,
                    status="active",
                )
            else:
                # No active connection - generate URL with helper actions for on-demand auth
                generated_url = await self.api_client.generate_mcp_url(
                    mcp_server_id=server_info.server_id, user_ids=[entity_id]
                )

                # Use the user URL and add helper actions parameter
                base_url = (
                    generated_url.user_ids_url[0]
                    if generated_url.user_ids_url
                    else generated_url.mcp_url
                )
                final_url = f"{base_url}&include_composio_helper_actions=true"

                return UserMCPConnection(
                    app_key=app_key,
                    user_id=user_id,
                    mcp_url=final_url,
                    connected_account_id=None,
                    server_id=server_info.server_id,
                    status="pending",
                )

        except Exception as e:
            logger.error(f"Error generating user MCP URL for {user_id}, {app_key}: {e}")
            return UserMCPConnection(
                app_key=app_key,
                user_id=user_id,
                mcp_url="",
                connected_account_id=None,
                server_id="",
                status="error",
            )

    def _map_user_to_entity_id(self, user_id: str) -> str:
        """
        Map Atlas user ID to Composio entity ID.
        For now, we use the user ID directly, but this could be enhanced
        to use a different mapping strategy if needed.
        """
        return user_id

    async def _get_stored_server_info(self, app_key: str) -> Optional[MCPServerInfo]:
        """Get stored MCP server info from our database"""
        try:
            # For now, we'll store this in a simple JSON format in the agents table
            # In the future, this could be moved to a dedicated table

            # This is a placeholder - we'll implement proper storage later
            # For now, return None to force creation via API
            return None

        except Exception as e:
            logger.error(f"Error getting stored server info for {app_key}: {e}")
            return None

    async def _store_server_info(self, server_info: MCPServerInfo) -> bool:
        """Store MCP server info in our database"""
        try:
            # For now, we'll store this in a simple format
            # In the future, this could be moved to a dedicated table

            logger.info(
                f"Storing server info for {server_info.app_key}: {server_info.server_id}"
            )
            # This is a placeholder - we'll implement proper storage later
            return True

        except Exception as e:
            logger.error(f"Error storing server info: {e}")
            return False

    async def list_supported_apps(self) -> List[str]:
        """
        Get list of supported apps.
        This could be enhanced to dynamically fetch from Composio API.
        """
        # For now, return a curated list of popular apps
        return [
            "gmail",
            "github",
            "slack",
            "notion",
            "linear",
            "jira",
            "trello",
            "google-drive",
            "google-sheets",
            "google-calendar",
            "dropbox",
            "salesforce",
            "hubspot",
            "zendesk",
            "stripe",
        ]


# Global instance
composio_mcp_manager = ComposioMCPManager()
