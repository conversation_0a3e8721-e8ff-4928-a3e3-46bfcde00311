"""
Composio MCP Manager Service

This service implements the correct Composio MCP flow:
1. Create/retrieve MCP servers via API
2. Manage connected accounts for users
3. Generate user-specific URLs with proper query parameters

This replaces the static constants-based approach with proper API integration.
"""

import json
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from utils.logger import logger
from supabase import create_client, Client
from utils.config import config
from services.composio_api_client import (
    composio_api_client,
    ComposioMCPServer,
    ComposioConnectedAccount,
)
import os


# Supabase configuration
SUPABASE_URL = config.SUPABASE_URL
SUPABASE_SERVICE_KEY = config.SUPABASE_SERVICE_ROLE_KEY


@dataclass
class MCPServerConfig:
    """Configuration for an MCP server created in Composio dashboard"""

    app_key: str
    server_id: str
    server_name: str
    base_mcp_url: str
    description: str


@dataclass
class UserMCPConnection:
    """User-specific MCP connection with generated URL"""

    app_key: str
    user_id: str
    mcp_url: str
    connected_account_id: Optional[str]
    server_id: str
    status: str  # 'active', 'pending', 'error'


class ComposioMCPManager:
    """Manager for per-user MCP server instantiation"""

    def __init__(self):
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        self.api_client = composio_api_client

        # Static registry of MCP servers created in Composio dashboard
        self.mcp_server_registry = self._load_mcp_server_registry()

    def _load_mcp_server_registry(self) -> Dict[str, MCPServerConfig]:
        """
        Load MCP server registry from configuration file.
        These are servers you've created manually in the Composio dashboard.
        """
        try:
            config_path = os.path.join(
                os.path.dirname(__file__), "..", "config", "composio_mcp_servers.json"
            )

            if not os.path.exists(config_path):
                logger.warning(f"MCP server config file not found: {config_path}")
                return {}

            with open(config_path, "r") as f:
                config_data = json.load(f)

            registry = {}
            for app_key, server_data in config_data.get("servers", {}).items():
                # Skip placeholder entries
                if "your-" in server_data.get("server_id", ""):
                    logger.info(f"Skipping placeholder config for {app_key}")
                    continue

                registry[app_key] = MCPServerConfig(
                    app_key=server_data["app_key"],
                    server_id=server_data["server_id"],
                    server_name=server_data["server_name"],
                    base_mcp_url=server_data["base_mcp_url"],
                    description=server_data["description"],
                )

            logger.info(f"Loaded {len(registry)} MCP server configurations")
            return registry

        except Exception as e:
            logger.error(f"Error loading MCP server registry: {e}")
            return {}

    def get_mcp_server_config(self, app_key: str) -> Optional[MCPServerConfig]:
        """
        Get MCP server configuration for the given app.
        Returns None if the app is not configured in the registry.
        """
        return self.mcp_server_registry.get(app_key)

    async def get_user_connected_accounts(
        self, user_id: str, app_key: Optional[str] = None
    ) -> List[ComposioConnectedAccount]:
        """Get connected accounts for a user, optionally filtered by app"""
        try:
            # Map Atlas user ID to Composio entity ID
            entity_id = self._map_user_to_entity_id(user_id)

            toolkit_slugs = [app_key] if app_key else None

            accounts = await self.api_client.list_connected_accounts(
                user_ids=[entity_id],
                toolkit_slugs=toolkit_slugs,
                statuses=["ACTIVE"],  # Only get active connections
            )

            logger.info(f"Found {len(accounts)} connected accounts for user {user_id}")
            return accounts

        except Exception as e:
            logger.error(f"Error getting connected accounts for user {user_id}: {e}")
            return []

    async def generate_user_mcp_url(
        self, user_id: str, app_key: str
    ) -> UserMCPConnection:
        """
        Generate a user-specific MCP URL with proper query parameters.
        This implements the correct Composio flow.
        """
        try:
            # Step 1: Get MCP server config from registry
            server_config = self.get_mcp_server_config(app_key)
            if not server_config:
                logger.error(f"No MCP server configured for app: {app_key}")
                return UserMCPConnection(
                    app_key=app_key,
                    user_id=user_id,
                    mcp_url="",
                    connected_account_id=None,
                    server_id="",
                    status="error",
                )

            # Step 2: Get user's connected accounts for this app
            connected_accounts = await self.get_user_connected_accounts(
                user_id, app_key
            )

            # Step 3: Map Atlas user ID to Composio entity ID
            entity_id = self._map_user_to_entity_id(user_id)

            # Step 4: Generate user-specific URL
            if connected_accounts:
                # User has active connection - use connected_account_id
                connected_account = connected_accounts[0]  # Use first active connection

                generated_url = await self.api_client.generate_mcp_url(
                    mcp_server_id=server_config.server_id,
                    user_ids=[entity_id],
                    connected_account_ids=[connected_account.id],
                )

                # Use the connected account URL
                final_url = (
                    generated_url.connected_account_urls[0]
                    if generated_url.connected_account_urls
                    else generated_url.mcp_url
                )

                return UserMCPConnection(
                    app_key=app_key,
                    user_id=user_id,
                    mcp_url=final_url,
                    connected_account_id=connected_account.id,
                    server_id=server_config.server_id,
                    status="active",
                )
            else:
                # No active connection - generate URL with helper actions for on-demand auth
                generated_url = await self.api_client.generate_mcp_url(
                    mcp_server_id=server_config.server_id, user_ids=[entity_id]
                )

                # Use the user URL and add helper actions parameter
                base_url = (
                    generated_url.user_ids_url[0]
                    if generated_url.user_ids_url
                    else generated_url.mcp_url
                )
                final_url = f"{base_url}&include_composio_helper_actions=true"

                return UserMCPConnection(
                    app_key=app_key,
                    user_id=user_id,
                    mcp_url=final_url,
                    connected_account_id=None,
                    server_id=server_config.server_id,
                    status="pending",
                )

        except Exception as e:
            logger.error(f"Error generating user MCP URL for {user_id}, {app_key}: {e}")
            return UserMCPConnection(
                app_key=app_key,
                user_id=user_id,
                mcp_url="",
                connected_account_id=None,
                server_id="",
                status="error",
            )

    def _map_user_to_entity_id(self, user_id: str) -> str:
        """
        Map Atlas user ID to Composio entity ID.
        For now, we use the user ID directly, but this could be enhanced
        to use a different mapping strategy if needed.
        """
        return user_id

    def add_mcp_server_config(self, config: MCPServerConfig) -> None:
        """
        Add a new MCP server configuration to the registry.
        Use this to register servers you've created in the Composio dashboard.
        """
        self.mcp_server_registry[config.app_key] = config
        logger.info(f"Added MCP server config for {config.app_key}: {config.server_id}")

    def list_configured_apps(self) -> List[str]:
        """
        Get list of apps that have MCP servers configured in the registry.
        """
        return list(self.mcp_server_registry.keys())

    async def list_supported_apps(self) -> List[str]:
        """
        Get list of supported apps based on configured MCP servers.
        """
        return self.list_configured_apps()

    async def store_user_mcp_connection(
        self, user_id: str, connection: UserMCPConnection
    ) -> bool:
        """
        Store user MCP connection in the default agent's custom_mcps.
        This integrates with the existing storage mechanism.
        """
        try:
            logger.info(
                f"Storing MCP connection for user {user_id}, app {connection.app_key}"
            )

            # Normalize user ID to proper UUID format
            actual_user_id = self._normalize_user_id(user_id)

            # Get account_id from user_id using basejump schema
            account_result = (
                self.supabase.schema("basejump")
                .table("accounts")
                .select("id")
                .eq("primary_owner_user_id", actual_user_id)
                .eq("personal_account", True)
                .execute()
            )

            if not account_result.data:
                logger.error(f"No account found for user {actual_user_id}")
                return False

            account_id = account_result.data[0]["id"]

            # Get default agent for this account
            agent_result = (
                self.supabase.table("agents")
                .select("agent_id, custom_mcps")
                .eq("account_id", account_id)
                .eq("is_default", True)
                .execute()
            )

            if not agent_result.data:
                logger.error(f"No default agent found for account {account_id}")
                return False

            default_agent = agent_result.data[0]
            default_agent_id = default_agent["agent_id"]
            current_custom_mcps = default_agent.get("custom_mcps", [])

            # Create MCP config in the format expected by the agents table
            new_mcp_config = {
                "name": connection.app_key.title(),
                "type": "http",
                "config": {"url": connection.mcp_url},
                "enabledTools": [],  # Will be populated when user selects tools
                "metadata": {
                    "app_key": connection.app_key,
                    "server_id": connection.server_id,
                    "connected_account_id": connection.connected_account_id,
                    "status": connection.status,
                    "created_at": "now()",
                },
            }

            # Check if MCP already exists and update or add
            existing_index = None
            for i, mcp in enumerate(current_custom_mcps):
                if (
                    mcp.get("type") == "http"
                    and mcp.get("metadata", {}).get("app_key") == connection.app_key
                ):
                    existing_index = i
                    break

            if existing_index is not None:
                # Update existing entry
                current_custom_mcps[existing_index] = new_mcp_config
                logger.info(f"Updated existing MCP for {connection.app_key}")
            else:
                # Add new entry
                current_custom_mcps.append(new_mcp_config)
                logger.info(f"Added new MCP for {connection.app_key}")

            # Update the default agent with new custom_mcps
            update_result = (
                self.supabase.table("agents")
                .update({"custom_mcps": current_custom_mcps})
                .eq("agent_id", default_agent_id)
                .execute()
            )

            if not update_result.data:
                logger.error(f"Failed to update default agent {default_agent_id}")
                return False

            logger.info(f"Successfully stored MCP connection for {connection.app_key}")
            return True

        except Exception as e:
            logger.error(f"Error storing MCP connection: {e}")
            return False

    def _normalize_user_id(self, user_id: str) -> str:
        """
        Normalize user ID to proper UUID format.
        This handles test user IDs and ensures proper UUID format.
        """
        import uuid

        # If it's already a valid UUID, return as-is
        try:
            uuid.UUID(user_id)
            return user_id
        except ValueError:
            pass

        # For test users, generate a consistent UUID
        if user_id.startswith("test-user"):
            # Generate a consistent UUID based on the test user ID
            import hashlib

            hash_object = hashlib.md5(user_id.encode())
            hex_dig = hash_object.hexdigest()
            # Convert to UUID format
            return f"{hex_dig[:8]}-{hex_dig[8:12]}-{hex_dig[12:16]}-{hex_dig[16:20]}-{hex_dig[20:32]}"

        # For other cases, generate a random UUID
        return str(uuid.uuid4())


# Global instance
composio_mcp_manager = ComposioMCPManager()
