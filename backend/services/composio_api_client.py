"""
Composio API Client Service

This service provides a proper HTTP client for interacting with the Composio API
to create MCP servers, manage connected accounts, and generate user-specific URLs.

This replaces the static constants-based approach with dynamic API calls.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from utils.logger import logger
from utils.config import config


@dataclass
class ComposioMCPServer:
    """Represents a Composio MCP server"""
    id: str
    name: str
    mcp_url: str
    auth_config_ids: List[str]
    allowed_tools: List[str]
    toolkits: List[str]
    commands: Dict[str, str]
    created_at: str
    updated_at: str
    managed_auth_via_composio: bool


@dataclass
class ComposioConnectedAccount:
    """Represents a connected account in Composio"""
    id: str
    toolkit_slug: str
    status: str  # ACTIVE, INITIALIZING, etc.
    created_at: str
    updated_at: str
    auth_config_id: str


@dataclass
class ComposioGeneratedURL:
    """Represents a generated MCP URL with user-specific parameters"""
    mcp_url: str
    connected_account_urls: List[str]
    user_ids_url: List[str]


class ComposioAPIClient:
    """HTTP client for Composio API operations"""
    
    def __init__(self):
        self.base_url = config.COMPOSIO_API_BASE or "https://backend.composio.dev/api/v3"
        self.api_key = config.COMPOSIO_API_KEY
        self.timeout = 30.0
        
        if not self.api_key:
            logger.warning("COMPOSIO_API_KEY not configured - Composio API operations will fail")
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Composio API requests"""
        return {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an HTTP request to the Composio API"""
        if not self.api_key:
            raise ValueError("COMPOSIO_API_KEY not configured")
        
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        logger.info(f"Making {method} request to {url}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            try:
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                ) as response:
                    response_text = await response.text()
                    logger.info(f"Composio API response status: {response.status}")
                    
                    if response.status >= 400:
                        logger.error(f"Composio API error: {response.status} - {response_text}")
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=response_text
                        )
                    
                    try:
                        return json.loads(response_text)
                    except json.JSONDecodeError:
                        logger.error(f"Invalid JSON response: {response_text}")
                        raise ValueError(f"Invalid JSON response from Composio API")
                        
            except asyncio.TimeoutError:
                logger.error(f"Timeout making request to {url}")
                raise
            except Exception as e:
                logger.error(f"Error making request to {url}: {e}")
                raise
    
    async def create_mcp_server(
        self, 
        name: str, 
        toolkits: List[str],
        auth_config_ids: Optional[List[str]] = None,
        allowed_tools: Optional[List[str]] = None
    ) -> ComposioMCPServer:
        """Create a new MCP server via Composio API"""
        data = {
            "name": name,
            "toolkits": toolkits
        }
        
        if auth_config_ids:
            data["auth_config_ids"] = auth_config_ids
        
        if allowed_tools:
            data["allowed_tools"] = allowed_tools
        
        # Use the custom endpoint for multi-app servers
        response = await self._make_request("POST", "/mcp/servers/custom", data)
        
        return ComposioMCPServer(
            id=response["id"],
            name=response["name"],
            mcp_url=response["mcp_url"],
            auth_config_ids=response.get("auth_config_ids", []),
            allowed_tools=response.get("allowed_tools", []),
            toolkits=toolkits,
            commands=response.get("commands", {}),
            created_at=response.get("created_at", ""),
            updated_at=response.get("updated_at", ""),
            managed_auth_via_composio=response.get("managed_auth_via_composio", True)
        )
    
    async def list_mcp_servers(
        self, 
        name: Optional[str] = None,
        toolkits: Optional[List[str]] = None
    ) -> List[ComposioMCPServer]:
        """List existing MCP servers"""
        params = {}
        if name:
            params["name"] = name
        if toolkits:
            params["toolkits"] = ",".join(toolkits)
        
        response = await self._make_request("GET", "/mcp/servers", params=params)
        
        servers = []
        for item in response.get("items", []):
            servers.append(ComposioMCPServer(
                id=item["id"],
                name=item["name"],
                mcp_url=item["mcp_url"],
                auth_config_ids=item.get("auth_config_ids", []),
                allowed_tools=item.get("allowed_tools", []),
                toolkits=item.get("toolkits", []),
                commands=item.get("commands", {}),
                created_at=item.get("created_at", ""),
                updated_at=item.get("updated_at", ""),
                managed_auth_via_composio=item.get("managed_auth_via_composio", True)
            ))
        
        return servers
    
    async def generate_mcp_url(
        self,
        mcp_server_id: str,
        user_ids: List[str],
        connected_account_ids: Optional[List[str]] = None
    ) -> ComposioGeneratedURL:
        """Generate user-specific MCP URLs"""
        data = {
            "mcp_server_id": mcp_server_id,
            "user_ids": user_ids
        }
        
        if connected_account_ids:
            data["connected_account_ids"] = connected_account_ids
        
        response = await self._make_request("POST", "/mcp/servers/generate", data)
        
        return ComposioGeneratedURL(
            mcp_url=response["mcp_url"],
            connected_account_urls=response.get("connected_account_urls", []),
            user_ids_url=response.get("user_ids_url", [])
        )
    
    async def list_connected_accounts(
        self,
        user_ids: Optional[List[str]] = None,
        toolkit_slugs: Optional[List[str]] = None,
        statuses: Optional[List[str]] = None
    ) -> List[ComposioConnectedAccount]:
        """List connected accounts for users"""
        params = {}
        if user_ids:
            params["user_ids"] = user_ids
        if toolkit_slugs:
            params["toolkit_slugs"] = toolkit_slugs
        if statuses:
            params["statuses"] = statuses
        
        response = await self._make_request("GET", "/connected_accounts", params=params)
        
        accounts = []
        for item in response.get("items", []):
            accounts.append(ComposioConnectedAccount(
                id=item["id"],
                toolkit_slug=item.get("toolkit", {}).get("slug", ""),
                status=item["status"],
                created_at=item.get("created_at", ""),
                updated_at=item.get("updated_at", ""),
                auth_config_id=item.get("auth_config", {}).get("id", "")
            ))
        
        return accounts


# Global instance
composio_api_client = ComposioAPIClient()
