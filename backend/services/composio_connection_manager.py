"""
Composio Connection Manager Service

This service manages Composio connected accounts using the official Composio SDK.
It handles:
- Connected account retrieval and management
- Connection ID mapping for users
- Entity ID management for multi-tenant support
- Connection status monitoring

This is separate from MCP server management and focuses on user connections.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from utils.logger import logger
from utils.config import config
import asyncio


@dataclass
class ComposioConnection:
    """Represents a Composio connected account"""
    id: str
    app_name: str
    status: str  # ACTIVE, INITIALIZING, etc.
    entity_id: str
    created_at: str
    updated_at: str
    auth_config_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ConnectionStatus:
    """Status of a user's connection to an app"""
    user_id: str
    app_key: str
    has_connection: bool
    connection_id: Optional[str] = None
    connection_status: Optional[str] = None
    entity_id: Optional[str] = None


class ComposioConnectionManager:
    """Manager for Composio connected accounts using official SDK"""
    
    def __init__(self):
        self.api_key = config.COMPOSIO_API_KEY
        self._toolset = None
        
        if not self.api_key:
            logger.warning("COMPOSIO_API_KEY not configured - connection management will be limited")
    
    def _get_toolset(self):
        """Get or create Composio toolset instance"""
        if self._toolset is None:
            try:
                from composio import ComposioToolSet
                self._toolset = ComposioToolSet(api_key=self.api_key)
                logger.info("Initialized Composio toolset")
            except ImportError:
                logger.error("Composio SDK not installed. Run: pip install composio-core")
                raise
            except Exception as e:
                logger.error(f"Failed to initialize Composio toolset: {e}")
                raise
        
        return self._toolset
    
    def _map_user_to_entity_id(self, user_id: str) -> str:
        """
        Map Atlas user ID to Composio entity ID.
        
        For multi-tenant support, each Atlas user should have a unique entity ID
        in Composio to isolate their connections and data.
        """
        # For now, use the user ID directly as entity ID
        # In production, you might want to use a different mapping strategy
        return user_id
    
    async def get_user_connections(
        self, 
        user_id: str, 
        app_name: Optional[str] = None
    ) -> List[ComposioConnection]:
        """
        Get connected accounts for a specific user.
        
        Args:
            user_id: Atlas user ID
            app_name: Optional app name to filter by (e.g., "gmail", "slack")
        
        Returns:
            List of user's connected accounts
        """
        try:
            if not self.api_key:
                logger.warning("COMPOSIO_API_KEY not configured")
                return []
            
            toolset = self._get_toolset()
            entity_id = self._map_user_to_entity_id(user_id)
            
            logger.info(f"Getting connections for user {user_id} (entity: {entity_id})")
            
            # Get connected accounts for the entity
            connections = toolset.get_connected_accounts(entity_id=entity_id)
            
            result = []
            for conn in connections:
                # Filter by app name if specified
                if app_name and conn.appName.lower() != app_name.lower():
                    continue
                
                composio_conn = ComposioConnection(
                    id=conn.id,
                    app_name=conn.appName,
                    status=conn.status,
                    entity_id=entity_id,
                    created_at=getattr(conn, 'createdAt', ''),
                    updated_at=getattr(conn, 'updatedAt', ''),
                    auth_config_id=getattr(conn, 'authConfigId', None),
                    metadata=getattr(conn, 'metadata', {})
                )
                result.append(composio_conn)
            
            logger.info(f"Found {len(result)} connections for user {user_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting connections for user {user_id}: {e}")
            return []
    
    async def get_connection_details(self, connection_id: str) -> Optional[ComposioConnection]:
        """
        Get detailed information about a specific connection.
        
        Args:
            connection_id: Composio connection ID (e.g., "ca_PU0-B9237c2v")
        
        Returns:
            Connection details or None if not found
        """
        try:
            if not self.api_key:
                logger.warning("COMPOSIO_API_KEY not configured")
                return None
            
            toolset = self._get_toolset()
            
            logger.info(f"Getting details for connection {connection_id}")
            
            # Get specific connected account
            conn = toolset.get_connected_account(connection_id)
            
            if not conn:
                logger.warning(f"Connection {connection_id} not found")
                return None
            
            return ComposioConnection(
                id=conn.id,
                app_name=conn.appName,
                status=conn.status,
                entity_id=getattr(conn, 'entityId', ''),
                created_at=getattr(conn, 'createdAt', ''),
                updated_at=getattr(conn, 'updatedAt', ''),
                auth_config_id=getattr(conn, 'authConfigId', None),
                metadata=getattr(conn, 'metadata', {})
            )
            
        except Exception as e:
            logger.error(f"Error getting connection details for {connection_id}: {e}")
            return None
    
    async def check_user_connection_status(
        self, 
        user_id: str, 
        app_key: str
    ) -> ConnectionStatus:
        """
        Check if a user has an active connection to a specific app.
        
        Args:
            user_id: Atlas user ID
            app_key: App key (e.g., "gmail", "slack")
        
        Returns:
            Connection status information
        """
        try:
            connections = await self.get_user_connections(user_id, app_key)
            
            # Look for active connections
            active_connections = [conn for conn in connections if conn.status == "ACTIVE"]
            
            if active_connections:
                # Use the first active connection
                conn = active_connections[0]
                return ConnectionStatus(
                    user_id=user_id,
                    app_key=app_key,
                    has_connection=True,
                    connection_id=conn.id,
                    connection_status=conn.status,
                    entity_id=conn.entity_id
                )
            else:
                return ConnectionStatus(
                    user_id=user_id,
                    app_key=app_key,
                    has_connection=False,
                    entity_id=self._map_user_to_entity_id(user_id)
                )
                
        except Exception as e:
            logger.error(f"Error checking connection status for user {user_id}, app {app_key}: {e}")
            return ConnectionStatus(
                user_id=user_id,
                app_key=app_key,
                has_connection=False,
                entity_id=self._map_user_to_entity_id(user_id)
            )
    
    async def list_all_user_connections(self, user_id: str) -> List[ComposioConnection]:
        """
        Get all connected accounts for a user across all apps.
        
        Args:
            user_id: Atlas user ID
        
        Returns:
            List of all user's connections
        """
        return await self.get_user_connections(user_id)
    
    def is_sdk_available(self) -> bool:
        """Check if Composio SDK is available and configured"""
        try:
            if not self.api_key:
                return False
            
            self._get_toolset()
            return True
        except Exception:
            return False


# Global instance
composio_connection_manager = ComposioConnectionManager()
