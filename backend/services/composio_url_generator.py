"""
Composio URL Generator Service

This service handles the two-phase URL generation for Composio MCP:
1. Phase 1: Connection Helper URLs (for OAuth authentication)
2. Phase 2: Tool Calling URLs (for actual MCP tool execution)

The key insight is that these serve different purposes:
- Helper URLs: Guide users through OAuth flow
- Tool Calling URLs: Used by Atlas to make actual MCP calls
"""

from typing import Optional
from dataclasses import dataclass
from utils.logger import logger
from enum import Enum


class URLPhase(Enum):
    """Represents the phase of URL usage"""
    CONNECTION_CREATION = "connection_creation"  # For OAuth flow
    TOOL_CALLING = "tool_calling"               # For actual MCP calls


@dataclass
class ComposioURL:
    """Represents a Composio URL with metadata"""
    url: str
    phase: URLPhase
    server_id: str
    user_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    description: str = ""


class ComposioURLGenerator:
    """Generator for Composio MCP URLs in different phases"""
    
    def __init__(self):
        self.base_mcp_domain = "https://mcp.composio.dev"
    
    def generate_connection_helper_url(
        self, 
        server_id: str, 
        user_id: str
    ) -> ComposioURL:
        """
        Generate Phase 1 URL: Connection Helper URL for OAuth authentication.
        
        This URL is used to guide users through the OAuth flow and includes
        helper actions that assist with authentication.
        
        Args:
            server_id: Composio MCP server ID
            user_id: Atlas user ID (mapped to entity_id)
        
        Returns:
            ComposioURL for connection creation phase
        """
        url = f"{self.base_mcp_domain}/composio/server/{server_id}?user_id={user_id}&include_composio_helper_actions=true"
        
        return ComposioURL(
            url=url,
            phase=URLPhase.CONNECTION_CREATION,
            server_id=server_id,
            user_id=user_id,
            description="Helper URL for OAuth authentication flow"
        )
    
    def generate_tool_calling_url(
        self, 
        server_id: str, 
        connected_account_id: str
    ) -> ComposioURL:
        """
        Generate Phase 2 URL: Tool Calling URL for actual MCP operations.
        
        This URL is used by Atlas to make actual MCP tool calls after the user
        has completed OAuth authentication.
        
        Args:
            server_id: Composio MCP server ID
            connected_account_id: Composio connection ID (e.g., "ca_PU0-B9237c2v")
        
        Returns:
            ComposioURL for tool calling phase
        """
        url = f"{self.base_mcp_domain}/composio/server/{server_id}/mcp?connected_account_id={connected_account_id}"
        
        return ComposioURL(
            url=url,
            phase=URLPhase.TOOL_CALLING,
            server_id=server_id,
            connected_account_id=connected_account_id,
            description="Tool calling URL for MCP operations"
        )
    
    def generate_user_specific_url(
        self, 
        server_id: str, 
        user_id: str, 
        connected_account_id: Optional[str] = None
    ) -> ComposioURL:
        """
        Generate appropriate URL based on user's connection status.
        
        If user has a connected account, returns tool calling URL.
        If user doesn't have a connected account, returns helper URL.
        
        Args:
            server_id: Composio MCP server ID
            user_id: Atlas user ID
            connected_account_id: Optional connection ID if user is connected
        
        Returns:
            Appropriate ComposioURL for the user's current state
        """
        if connected_account_id:
            # User has connection - return tool calling URL
            return self.generate_tool_calling_url(server_id, connected_account_id)
        else:
            # User needs to connect - return helper URL
            return self.generate_connection_helper_url(server_id, user_id)
    
    def extract_server_id_from_url(self, url: str) -> Optional[str]:
        """
        Extract server ID from a Composio URL.
        
        Args:
            url: Composio URL
        
        Returns:
            Server ID if found, None otherwise
        """
        try:
            # Handle both URL formats:
            # https://mcp.composio.dev/composio/server/{server_id}?...
            # https://mcp.composio.dev/composio/server/{server_id}/mcp?...
            
            if "/composio/server/" not in url:
                return None
            
            # Extract the part after "/composio/server/"
            parts = url.split("/composio/server/")[1]
            
            # Handle both formats
            if "/mcp?" in parts:
                # Tool calling URL format
                server_id = parts.split("/mcp?")[0]
            elif "?" in parts:
                # Helper URL format
                server_id = parts.split("?")[0]
            else:
                # Base URL without parameters
                server_id = parts.rstrip("/")
            
            return server_id if server_id else None
            
        except Exception as e:
            logger.error(f"Error extracting server ID from URL {url}: {e}")
            return None
    
    def extract_connected_account_id_from_url(self, url: str) -> Optional[str]:
        """
        Extract connected account ID from a tool calling URL.
        
        Args:
            url: Composio tool calling URL
        
        Returns:
            Connected account ID if found, None otherwise
        """
        try:
            if "connected_account_id=" not in url:
                return None
            
            # Extract the connected_account_id parameter
            parts = url.split("connected_account_id=")[1]
            account_id = parts.split("&")[0]  # Handle additional parameters
            
            return account_id if account_id else None
            
        except Exception as e:
            logger.error(f"Error extracting connected account ID from URL {url}: {e}")
            return None
    
    def identify_url_phase(self, url: str) -> URLPhase:
        """
        Identify which phase a URL belongs to.
        
        Args:
            url: Composio URL
        
        Returns:
            URLPhase enum value
        """
        if "/mcp?" in url and "connected_account_id=" in url:
            return URLPhase.TOOL_CALLING
        elif "include_composio_helper_actions=true" in url:
            return URLPhase.CONNECTION_CREATION
        else:
            # Default to connection creation for unknown formats
            return URLPhase.CONNECTION_CREATION
    
    def is_valid_composio_url(self, url: str) -> bool:
        """
        Check if a URL is a valid Composio MCP URL.
        
        Args:
            url: URL to validate
        
        Returns:
            True if valid Composio URL, False otherwise
        """
        try:
            return (
                url.startswith(self.base_mcp_domain) and
                "/composio/server/" in url and
                self.extract_server_id_from_url(url) is not None
            )
        except Exception:
            return False
    
    def upgrade_helper_url_to_tool_calling(
        self, 
        helper_url: str, 
        connected_account_id: str
    ) -> Optional[ComposioURL]:
        """
        Upgrade a helper URL to a tool calling URL after OAuth completion.
        
        Args:
            helper_url: Original helper URL
            connected_account_id: New connection ID from OAuth
        
        Returns:
            New tool calling URL or None if upgrade failed
        """
        try:
            server_id = self.extract_server_id_from_url(helper_url)
            if not server_id:
                logger.error(f"Could not extract server ID from helper URL: {helper_url}")
                return None
            
            return self.generate_tool_calling_url(server_id, connected_account_id)
            
        except Exception as e:
            logger.error(f"Error upgrading helper URL to tool calling URL: {e}")
            return None


# Global instance
composio_url_generator = ComposioURLGenerator()
