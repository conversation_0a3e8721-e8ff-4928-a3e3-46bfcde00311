#!/usr/bin/env python3
"""
Test script for real MCP servers

This script tests the actual Gmail and Notion servers you've configured.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from services.composio_mcp_manager import composio_mcp_manager
from utils.logger import logger


async def test_real_servers():
    """Test with real Gmail and Notion servers"""
    print("🧪 Testing Real MCP Servers")
    print("=" * 50)
    
    test_user_id = "test-user-789"
    
    # Test Gmail
    print("\n📧 Testing Gmail Server")
    gmail_connection = await composio_mcp_manager.generate_user_mcp_url(
        test_user_id, "gmail"
    )
    
    print(f"Status: {gmail_connection.status}")
    print(f"MCP URL: {gmail_connection.mcp_url}")
    print(f"Server ID: {gmail_connection.server_id}")
    print(f"Connected Account ID: {gmail_connection.connected_account_id}")
    
    # Test Notion
    print("\n📝 Testing Notion Server")
    notion_connection = await composio_mcp_manager.generate_user_mcp_url(
        test_user_id, "notion"
    )
    
    print(f"Status: {notion_connection.status}")
    print(f"MCP URL: {notion_connection.mcp_url}")
    print(f"Server ID: {notion_connection.server_id}")
    print(f"Connected Account ID: {notion_connection.connected_account_id}")
    
    return gmail_connection.status != "error" and notion_connection.status != "error"


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    success = asyncio.run(test_real_servers())
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
